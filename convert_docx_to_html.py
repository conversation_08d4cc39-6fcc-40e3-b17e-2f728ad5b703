#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحويل ملف Word إلى HTML مع الحفاظ على التنسيق
"""

import mammoth
import os
from pathlib import Path

def convert_docx_to_html(docx_file, output_file=None):
    """
    تحويل ملف DOCX إلى HTML
    
    Args:
        docx_file (str): مسار ملف Word
        output_file (str): مسار ملف HTML المخرج (اختياري)
    
    Returns:
        str: محتوى HTML
    """
    
    # التحقق من وجود الملف
    if not os.path.exists(docx_file):
        raise FileNotFoundError(f"الملف غير موجود: {docx_file}")
    
    # تحديد اسم ملف الإخراج إذا لم يتم تحديده
    if output_file is None:
        base_name = Path(docx_file).stem
        output_file = f"{base_name}_converted.html"
    
    print(f"جاري تحويل {docx_file} إلى {output_file}...")
    
    try:
        # تحويل الملف
        with open(docx_file, "rb") as docx_file_obj:
            result = mammoth.convert_to_html(docx_file_obj)
            html_content = result.value
            
            # طباعة أي تحذيرات
            if result.messages:
                print("تحذيرات:")
                for message in result.messages:
                    print(f"  - {message}")
        
        # إنشاء HTML كامل مع CSS أساسي
        full_html = f"""<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحويل من Word</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 40px;
            background-color: #f9f9f9;
            color: #333;
        }}
        
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            border-radius: 8px;
        }}
        
        /* تنسيق الجداول */
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }}
        
        table, th, td {{
            border: 1px solid #ddd;
        }}
        
        th, td {{
            padding: 12px;
            text-align: right;
        }}
        
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        
        /* تنسيق الفقرات */
        p {{
            margin: 10px 0;
        }}
        
        /* تنسيق العناوين */
        h1, h2, h3, h4, h5, h6 {{
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
        }}
        
        /* تنسيق القوائم */
        ul, ol {{
            margin: 15px 0;
            padding-right: 30px;
        }}
        
        li {{
            margin: 5px 0;
        }}
        
        /* تنسيق النص المميز */
        strong {{
            font-weight: bold;
        }}
        
        em {{
            font-style: italic;
        }}
        
        /* تنسيق الصور */
        img {{
            max-width: 100%;
            height: auto;
            margin: 20px 0;
            border-radius: 4px;
        }}
        
        /* طباعة */
        @media print {{
            body {{
                margin: 0;
                background-color: white;
            }}
            
            .container {{
                box-shadow: none;
                margin: 0;
                padding: 20px;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        {html_content}
    </div>
</body>
</html>"""
        
        # حفظ الملف
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(full_html)
        
        print(f"تم التحويل بنجاح! الملف محفوظ في: {output_file}")
        return html_content
        
    except Exception as e:
        print(f"خطأ في التحويل: {str(e)}")
        raise

def main():
    """الدالة الرئيسية"""
    docx_file = "template.docx"
    output_file = "template_converted.html"
    
    try:
        convert_docx_to_html(docx_file, output_file)
        print("\n" + "="*50)
        print("تم التحويل بنجاح!")
        print(f"ملف HTML الجديد: {output_file}")
        print("="*50)
        
    except Exception as e:
        print(f"فشل التحويل: {str(e)}")

if __name__ == "__main__":
    main()
